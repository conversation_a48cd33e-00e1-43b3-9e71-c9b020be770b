import { ref, reactive, computed, h } from 'vue'
import { useMessage } from 'naive-ui'
import { NButton, NIcon } from 'naive-ui'
import { TrashOutline } from '@vicons/ionicons5'
import { useDictOptions, vehicleBrandUtils } from '@/utils/dictUtils'
import skuApi from '@/api/sku'
import salesPriceLimitRuleApi from '@/api/salesPriceLimitRule'

/**
 * 新增销售限价规则逻辑
 */
export function useAddSalesPriceLimitRule() {
    const message = useMessage()

    // 状态变量
    const visible = ref(false)
    const formRef = ref(null)
    const detailLoading = ref(false)
    const saving = ref(false)
    const vehicleSelectorVisible = ref(false)

    // 机构选择相关状态
    const showOrgSelector = ref(false)
    const selectedOrgs = ref([])

    // 表单数据
    const formData = reactive({
        ruleName: '',
        effectiveTime: null,
        expiryTime: null,
        ruleType: 'sku',
        priceLimit: null
    })

    // SKU表单数据
    const skuForm = reactive({
        vehicleBrand: null,
        vehicleModel: null,
        vehicleConfig: null
    })

    // 规则明细数据
    const ruleDetails = ref([])

    // 分页配置
    const pagination = computed(() => ({
        page: paginationState.page,
        pageSize: paginationState.pageSize,
        itemCount: ruleDetails.value.length,
        showSizePicker: true,
        pageSizes: [10, 20, 50],
        prefix: ({ itemCount }) => `共 ${itemCount} 条`,
        onChange: (page) => {
            paginationState.page = page
        },
        onUpdatePageSize: (pageSize) => {
            paginationState.pageSize = pageSize
            paginationState.page = 1 // 重置到第一页
        }
    }))

    // 分页状态
    const paginationState = reactive({
        page: 1,
        pageSize: 10
    })

    // 表单验证规则
    const formRules = {
        ruleName: [
            { required: true, message: '请输入规则名称', trigger: 'blur' }
        ],
        effectiveTime: [
            {
                required: true,
                message: '请选择生效时间',
                trigger: ['blur', 'change'],
                validator: (_, value) => {
                    if (!value) {
                        return new Error('请选择生效时间')
                    }
                    return true
                }
            }
        ],
        expiryTime: [
            {
                required: true,
                message: '请选择失效时间',
                trigger: ['blur', 'change'],
                validator: (_, value) => {
                    if (!value) {
                        return new Error('请选择失效时间')
                    }
                    // 检查失效时间是否晚于生效时间
                    if (formData.effectiveTime && value <= formData.effectiveTime) {
                        return new Error('失效时间必须晚于生效时间')
                    }
                    return true
                }
            }
        ],
        ruleType: [
            { required: true, message: '请选择规则类型', trigger: 'change' }
        ],
        priceLimit: [
            {
                required: true,
                message: '请输入销售限价',
                trigger: ['blur', 'change'],
                validator: (_, value) => {
                    if (value === null || value === undefined || value === '') {
                        return new Error('请输入销售限价')
                    }
                    if (typeof value === 'number' && value < 0) {
                        return new Error('限价金额不能小于0')
                    }
                    return true
                }
            }
        ]
    }

    // 车辆品牌选项（使用响应式字典数据）
    const { options: vehicleBrandOptions } = useDictOptions('vehicle_brand', false)

    // 车型选项（从API获取）
    const vehicleModelOptions = ref([])

    // 配置选项（从API获取）
    const vehicleConfigOptions = ref([])

    // 加载状态
    const loadingModels = ref(false)
    const loadingConfigs = ref(false)

    // 明细表格列配置
    const detailColumns = computed(() => {
        const baseColumns = [
            {
                title: '品牌',
                key: 'brandName',
                width: 120,
                render: (row) => {
                    return row.brandName || row.brand || '-'
                }
            },
            {
                title: '车型',
                key: 'modelName',
                width: 150,
                render: (row) => {
                    return row.modelName || row.series || '-'
                }
            },
            {
                title: '配置',
                key: 'configName',
                width: 200,
                render: (row) => {
                    return row.configName || '-'
                }
            }
        ]

        // 根据规则类型添加不同的列
        if (formData.ruleType === 'vin') {
            // VIN模式下添加VIN列
            baseColumns.push({
                title: 'VIN',
                key: 'vin',
                width: 180,
                render: (row) => {
                    return row.vin || '-'
                }
            })
        } else {
            // SKU模式下添加颜色代码列
            baseColumns.push({
                title: '颜色代码（内/外）',
                key: 'colorCode',
                width: 150,
                render: (row) => {
                    return row.colorCode || '-'
                }
            })
        }

        // 添加操作列
        baseColumns.push({
            title: '操作',
            key: 'actions',
            width: 100,
            align: 'center',
            render: (_, index) => {
                return h(
                    NButton,
                    {
                        size: 'small',
                        type: 'error',
                        text: true,
                        onClick: () => removeDetail(index),
                        style: {
                            padding: '4px'
                        }
                    },
                    {
                        default: () => h(
                            NIcon,
                            { size: 16 },
                            { default: () => h(TrashOutline) }
                        )
                    }
                )
            }
        })

        return baseColumns
    })

    // 计算是否可以查询SKU
    const canQuerySku = computed(() => {
        return skuForm.vehicleBrand // 只需要选择品牌即可查询
    })

    // 获取车型选项
    const fetchVehicleModels = async (brand) => {
        if (!brand) {
            vehicleModelOptions.value = []
            return
        }

        try {
            loadingModels.value = true
            const response = await skuApi.getSkuOptions('series', { brand })

            if (response.code === 200) {
                // 去重并转换为选项格式
                const uniqueSeries = [...new Set(response.data.map(item => item.series).filter(Boolean))]
                vehicleModelOptions.value = uniqueSeries.map(series => ({
                    label: series,
                    value: series
                }))
            } else {
                message.error(response.message || '获取车型失败')
                vehicleModelOptions.value = []
            }
        } catch (error) {
            console.error('获取车型失败:', error)
            message.error('获取车型失败')
            vehicleModelOptions.value = []
        } finally {
            loadingModels.value = false
        }
    }

    // 获取配置选项
    const fetchVehicleConfigs = async (brand, series) => {
        if (!brand || !series) {
            vehicleConfigOptions.value = []
            return
        }

        try {
            loadingConfigs.value = true
            const response = await skuApi.getSkuOptions('configName', { brand, series })

            if (response.code === 200) {
                // 去重并转换为选项格式
                const uniqueConfigs = [...new Set(response.data.map(item => item.configName).filter(Boolean))]
                vehicleConfigOptions.value = uniqueConfigs.map(configName => ({
                    label: configName,
                    value: configName
                }))
            } else {
                message.error(response.message || '获取配置失败')
                vehicleConfigOptions.value = []
            }
        } catch (error) {
            console.error('获取配置失败:', error)
            message.error('获取配置失败')
            vehicleConfigOptions.value = []
        } finally {
            loadingConfigs.value = false
        }
    }

    // 处理品牌变化
    const handleBrandChange = (value) => {
        skuForm.vehicleModel = null
        skuForm.vehicleConfig = null
        vehicleConfigOptions.value = []

        // 获取车型选项
        if (value) {
            fetchVehicleModels(value)
        } else {
            vehicleModelOptions.value = []
        }
    }

    // 处理车型变化
    const handleModelChange = (value) => {
        skuForm.vehicleConfig = null

        // 获取配置选项
        if (value && skuForm.vehicleBrand) {
            fetchVehicleConfigs(skuForm.vehicleBrand, value)
        } else {
            vehicleConfigOptions.value = []
        }
    }

    // 处理规则类型变化
    const handleRuleTypeChange = () => {
        // 如果有明细数据，先给用户提示
        if (ruleDetails.value.length > 0) {
            message.info('已清空规则明细列表')
        }

        // 清空规则明细列表
        ruleDetails.value = []
    }

    // 查询SKU
    const querySku = async () => {
        if (!canQuerySku.value) {
            message.warning('请选择车辆品牌')
            return
        }

        try {
            detailLoading.value = true

            // 构建查询参数
            const queryParams = {
                page: 1,
                size: 1000, // 获取所有匹配的SKU
                brand: skuForm.vehicleBrand,
                brands: undefined // 预定义brands属性
            }

            // 如果选择了车型，添加到查询参数
            if (skuForm.vehicleModel) {
                queryParams.series = skuForm.vehicleModel
            }

            // 如果选择了配置，添加到查询参数
            if (skuForm.vehicleConfig) {
                queryParams.configName = skuForm.vehicleConfig
            }

            // 添加品牌权限限制
            const cachedBrands = vehicleBrandUtils.getCachedBrands()
            if (cachedBrands && cachedBrands.length > 0) {
                queryParams.brands = cachedBrands.join(',')
            }

            // 调用真实的SKU查询API
            const response = await skuApi.getSkuList(queryParams)

            if (response.code === 200) {
                // 直接显示所有SKU数据，不进行分组
                ruleDetails.value = response.data.list.map((sku, index) => ({
                    id: Date.now() + index,
                    type: 'sku',
                    brandName: sku.brand,
                    modelName: sku.series || '',
                    configName: sku.configName || '',
                    colorCode: sku.colorCode || '',
                    skuId: sku.id, // 使用sku.id而不是sku.skuId
                    priceLimit: formData.priceLimit || 0
                }))

                message.success(`查询到 ${ruleDetails.value.length} 条SKU数据`)
            } else {
                message.error(response.data || '查询SKU失败')
                ruleDetails.value = []
            }

        } catch (error) {
            console.error('查询SKU失败:', error)
            message.error('查询SKU失败')
            ruleDetails.value = []
        } finally {
            detailLoading.value = false
        }
    }

    // 显示车辆选择器
    const showVehicleSelector = () => {
        vehicleSelectorVisible.value = true
    }

    // 处理车辆选择
    const handleVehicleSelected = (vehicles) => {
        vehicles.forEach(vehicle => {
            const newDetail = {
                id: Date.now() + Math.random(),
                type: 'vin',
                vin: vehicle.vin,
                brandName: vehicle.brand || vehicle.brandName || '',
                modelName: vehicle.series || vehicle.modelName || '',
                configName: vehicle.configName || '',
                priceLimit: formData.priceLimit
            }
            ruleDetails.value.push(newDetail)
        })

        message.success(`已添加 ${vehicles.length} 条VIN规则`)
    }

    // 处理车辆选择器取消
    const handleVehicleSelectorCancel = () => {
        // 取消操作，不需要特殊处理
    }

    // 处理机构选择
    const handleOrgSelect = (orgs) => {
        if (orgs && orgs.length > 0) {
            // 多选模式，保存所有选中的机构
            selectedOrgs.value = [...orgs]
        }
        showOrgSelector.value = false
    }

    // 处理机构选择取消
    const handleOrgCancel = () => {
        showOrgSelector.value = false
    }

    // 移除指定机构
    const removeOrg = (orgId) => {
        selectedOrgs.value = selectedOrgs.value.filter(org => org.id !== orgId)
    }

    // 删除明细
    const removeDetail = (index) => {
        ruleDetails.value.splice(index, 1)
        message.success('删除成功')
    }

    // 处理取消
    const handleCancel = () => {
        visible.value = false
        resetForm()
    }

    // 处理保存
    const handleSave = async () => {
        await saveRule()
    }

    // 保存规则
    const saveRule = async () => {
        try {
            // 表单验证
            await formRef.value?.validate()

            if (ruleDetails.value.length === 0) {
                message.warning('请至少添加一条规则明细')
                return
            }

            saving.value = true

            // 构建保存数据 - 提取规则明细的核心字段
            const expandedRuleDetails = []
            ruleDetails.value.forEach(item => {
                if (item.type === 'sku') {
                    // SKU类型数据，只保留必要字段
                    expandedRuleDetails.push({
                        brandName: item.brandName,
                        modelName: item.modelName,
                        configName: item.configName,
                        colorCode: item.colorCode,
                        skuId: item.skuId
                    })
                } else if (item.type === 'vin') {
                    // VIN类型数据，只保留必要字段
                    expandedRuleDetails.push({
                        brandName: item.brandName,
                        modelName: item.modelName,
                        configName: item.configName,
                        vin: item.vin
                    })
                }
            })

            const saveData = {
                ruleName: formData.ruleName,
                effectiveTime: formData.effectiveTime,
                expiryTime: formData.expiryTime,
                ruleType: formData.ruleType,
                priceLimit: formData.priceLimit * 100,
                status: 'active', // 直接设置为active状态
                effectiveOrg: selectedOrgs.value.map(org => org.id).join(','), // 将选中的机构ID用逗号拼接
                ruleDetails: JSON.stringify(expandedRuleDetails) // 将规则明细转换为JSON字符串
            }

            // 调用真实API
            await salesPriceLimitRuleApi.createRule(saveData)

            // 由于 requests.js 已经处理了错误响应，这里只处理成功情况
            message.success('规则保存成功')
            visible.value = false
            resetForm()

        } catch (error) {
            // requests.js 已经显示了错误消息，这里只需要记录日志
            console.error('保存失败:', error)
        } finally {
            saving.value = false
        }
    }

    // 重置表单
    const resetForm = () => {
        Object.assign(formData, {
            ruleName: '',
            effectiveTime: null,
            expiryTime: null,
            ruleType: 'sku',
            priceLimit: null
        })

        Object.assign(skuForm, {
            vehicleBrand: null,
            vehicleModel: null,
            vehicleConfig: null
        })

        ruleDetails.value = []
        selectedOrgs.value = []
    }

    return {
        // 状态
        visible,
        formRef,
        formData,
        formRules,
        skuForm,
        ruleDetails,
        detailLoading,
        saving,
        vehicleSelectorVisible,
        pagination,
        loadingModels,
        loadingConfigs,

        // 机构选择相关状态
        showOrgSelector,
        selectedOrgs,

        // 选项数据
        vehicleBrandOptions,
        vehicleModelOptions,
        vehicleConfigOptions,
        detailColumns,

        // 计算属性
        canQuerySku,

        // 方法
        handleCancel,
        handleSave,
        handleBrandChange,
        handleModelChange,
        handleRuleTypeChange,
        querySku,
        showVehicleSelector,
        handleVehicleSelected,
        handleVehicleSelectorCancel,

        // 机构选择相关方法
        handleOrgSelect,
        handleOrgCancel,
        removeOrg,
    }
}
