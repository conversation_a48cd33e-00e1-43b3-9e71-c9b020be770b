import request from '@/utils/request'

/**
 * 销售限价规则API
 */
const salesPriceLimitRuleApi = {
  /**
   * 创建销售限价规则
   * @param {Object} data - 规则数据
   * @returns {Promise}
   */
  createRule(data) {
    return request({
      url: '/operation/sales/price/limit/rule',
      method: 'POST',
      data
    })
  },

  /**
   * 获取销售限价规则列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getRuleList(params) {
    return request({
      url: '/operation/sales/price/limit/rule',
      method: 'GET',
      params
    })
  },

  /**
   * 获取销售限价规则详情
   * @param {string|number} id - 规则ID
   * @returns {Promise}
   */
  getRuleDetail(id) {
    return request({
      url: `/operation/sales/price/limit/rule/${id}`,
      method: 'GET'
    })
  },

  /**
   * 更新销售限价规则
   * @param {string|number} id - 规则ID
   * @param {Object} data - 更新数据
   * @returns {Promise}
   */
  updateRule(id, data) {
    return request({
      url: `/operation/sales/price/limit/rule/${id}`,
      method: 'PUT',
      data
    })
  },

  /**
   * 删除销售限价规则
   * @param {string|number} id - 规则ID
   * @returns {Promise}
   */
  deleteRule(id) {
    return request({
      url: `/operation/sales/price/limit/rule/${id}`,
      method: 'DELETE'
    })
  },

  /**
   * 批量删除销售限价规则
   * @param {Array} ids - 规则ID数组
   * @returns {Promise}
   */
  batchDeleteRules(ids) {
    return request({
      url: '/operation/sales/price/limit/rule/batch',
      method: 'DELETE',
      data: { ids }
    })
  },

  /**
   * 启用/禁用销售限价规则
   * @param {string|number} id - 规则ID
   * @param {string} status - 状态：active/inactive
   * @returns {Promise}
   */
  updateRuleStatus(id, status) {
    return request({
      url: `/operation/sales/price/limit/rule/${id}/status`,
      method: 'PUT',
      data: { status }
    })
  }
}

export default salesPriceLimitRuleApi
